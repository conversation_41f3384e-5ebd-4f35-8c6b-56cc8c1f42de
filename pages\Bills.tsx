
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Bill, BillStatus, ContextAwarePageProps, Account, JournalEntry } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';
import Dropdown from '../components/ui/Dropdown';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import BillStatusSummary from '../components/bills/BillStatusSummary';
import BillFilterBar from '../components/bills/BillFilterBar';
import BillDetailModal from '../components/bills/BillDetailModal';
import BillFormModal from '../components/bills/BillFormModal';
import AITransactionModal from '../components/transactions/AITransactionModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { isAIAvailable } from '../services/geminiService';

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

const Bills: React.FC<ContextAwarePageProps> = ({ workspaceId, context, clearPageContext }) => {
  // Fetch data from Convex
  const allBills = useQuery(api.bills.getBills, { workspaceId: workspaceId as any });
  const accounts = useQuery(api.accounts.getAccounts, { workspaceId: workspaceId as any });
  const suppliers = useQuery(api.suppliers.getSuppliers, { workspaceId: workspaceId as any });

  // Mutations
  const updateBillStatus = useMutation(api.bills.updateBillStatus);
  const deleteBill = useMutation(api.bills.deleteBill);
  const createBill = useMutation(api.bills.createBill);
  const updateBill = useMutation(api.bills.updateBill);

  const isLoading = allBills === undefined || accounts === undefined || suppliers === undefined;

  const [selectedBill, setSelectedBill] = useState<Bill | null>(null);

  // Modal states
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [billToDelete, setBillToDelete] = useState<Bill | null>(null);
  const [isAITransactionModalOpen, setIsAITransactionModalOpen] = useState(false);
  const [initialAIPrompt, setInitialAIPrompt] = useState<string | undefined>(undefined);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | BillStatus>('All');

  useEffect(() => {
    if (context?.filter) {
      setSearchTerm(context.filter);
      clearPageContext();
    }
  }, [context, clearPageContext]);

  const filteredBills = useMemo(() => {
    return allBills
      .filter(bill => statusFilter === 'All' || bill.status === statusFilter)
      .filter(bill => {
        const term = searchTerm.toLowerCase();
        return !term || bill.vendor.toLowerCase().includes(term) || bill._id.toLowerCase().includes(term);
      });
  }, [allBills, searchTerm, statusFilter]);
  
  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const handleSave = async (billData: Omit<Bill, '_id' | '_creationTime' | 'workspaceId'>, id?: string) => {
    if (!suppliers) return;

    try {
      if (id) {
        await updateBill({
          billId: id as any,
          supplierId: billData.supplierId as any,
          billNumber: billData.billNumber || `BILL-${Date.now().toString().slice(-6)}`,
          issueDate: billData.issueDate,
          dueDate: billData.dueDate,
          amount: billData.amount,
          status: billData.status,
          items: billData.items || [],
          taxId: billData.taxId,
          taxRate: billData.taxRate,
        });
      } else {
        await createBill({
          workspaceId: workspaceId as any,
          supplierId: billData.supplierId as any,
          billNumber: billData.billNumber || `BILL-${Date.now().toString().slice(-6)}`,
          issueDate: billData.issueDate,
          dueDate: billData.dueDate,
          amount: billData.amount,
          status: billData.status,
          items: billData.items || [],
          taxId: billData.taxId,
          taxRate: billData.taxRate,
        });
      }
      setIsFormOpen(false);
    } catch (error) {
      console.error('Failed to save bill:', error);
    }
  };

  const handleMarkAsPaid = async (bill: Bill) => {
    try {
      await updateBillStatus({
        billId: bill._id as any,
        status: BillStatus.Paid
      });
    } catch (error) {
      console.error('Failed to mark bill as paid:', error);
    }
  };
  
  const handleView = (bill: Bill) => {
    setSelectedBill(bill);
    setIsDetailOpen(true);
  };
  
  const handleNew = () => {
    setSelectedBill(null);
    setIsFormOpen(true);
  };

  const handleEdit = (bill: Bill) => {
    setSelectedBill(bill);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (bill: Bill) => {
    setBillToDelete(bill);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (billToDelete) {
      try {
        await deleteBill({ billId: billToDelete._id as any });
        setBillToDelete(null);
      } catch (error) {
        console.error('Failed to delete bill:', error);
      }
    }
    setIsConfirmDeleteOpen(false);
  };
  
  const handleGenericAITransactionRequest = () => {
    setInitialAIPrompt(undefined);
    setIsAITransactionModalOpen(true);
  };

  const handleAITransactionRequest = (bill: Bill) => {
    const prompt = `Record payment for bill ${bill.description} from ${bill.vendor} for ${formatCurrency(bill.amount)}.`;
    setInitialAIPrompt(prompt);
    setIsAITransactionModalOpen(true);
  };

  const handleJournalEntrySave = async (entryData: Omit<JournalEntry, '_id' | '_creationTime' | 'workspaceId'>) => {
    mockData.journalEntries.push({
        ...entryData,
        _id: createId('je'),
        _creationTime: Date.now(),
        workspaceId,
    });
    setIsAITransactionModalOpen(false);
    setInitialAIPrompt(undefined);
  };

  const columns = [
    { key: 'vendor' as keyof Bill, header: 'Vendor' },
    { key: 'category' as keyof Bill, header: 'Category' },
    { key: 'date' as keyof Bill, header: 'Date' },
    { key: 'dueDate' as keyof Bill, header: 'Due Date' },
    {
      key: 'amount' as keyof Bill,
      header: 'Amount',
      render: (item: Bill) => <span className="font-medium">{formatCurrency(item.amount)}</span>,
    },
    {
      key: 'status' as keyof Bill,
      header: 'Status',
      render: (item: Bill) => <Badge status={item.status} />,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Bill) => (
        <div className="flex items-center justify-end space-x-2">
          {(item.status === BillStatus.Unpaid || item.status === BillStatus.Overdue) && (
              <Button variant="secondary" size="sm" onClick={() => handleMarkAsPaid(item)}>
                  Mark as Paid
              </Button>
          )}
           <Button variant="secondary" size="sm" onClick={() => handleAITransactionRequest(item)} disabled={!isAIAvailable} title={!isAIAvailable ? "AI features unavailable" : "Record transaction using AI"}>
            Record AI
          </Button>
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
              </Button>
            }
          >
            <div className="py-1">
              <button onClick={() => handleView(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                View
              </button>
              <button onClick={() => handleEdit(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Edit
              </button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleDeleteRequest(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-100">
                Delete
              </button>
            </div>
          </Dropdown>
        </div>
      )
    },
  ];
  
  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Bills & Expenses</h2>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleGenericAITransactionRequest} disabled={!isAIAvailable} title={aiDisabledTooltip}>Record with AI</Button>
          <Button variant="primary" onClick={handleNew}>New Bill</Button>
        </div>
      </div>
      
      <BillStatusSummary bills={allBills} isLoading={isLoading} />
      
      <BillFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        onClear={() => setSearchTerm('')}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(5)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Bill> columns={columns} data={filteredBills.map(b => ({...b, id: b._id}))} />
        )}
      </Card>

      <BillDetailModal 
        bill={selectedBill}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
      />
      
      <BillFormModal 
        bill={selectedBill}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedBill?._id)}
      />

       <AITransactionModal
        isOpen={isAITransactionModalOpen}
        onClose={() => setIsAITransactionModalOpen(false)}
        onSave={handleJournalEntrySave}
        accounts={accounts || []}
        workspaceId={workspaceId}
        initialPrompt={initialAIPrompt}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Bill"
        message={
            <span>
                Are you sure you want to delete the bill from <strong>{billToDelete?.vendor}</strong>? 
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Bill"
      />
    </div>
  );
};

export default Bills;
