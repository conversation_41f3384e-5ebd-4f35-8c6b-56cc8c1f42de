/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as accounts from "../accounts.js";
import type * as auth from "../auth.js";
import type * as bills from "../bills.js";
import type * as clients from "../clients.js";
import type * as companyProfiles from "../companyProfiles.js";
import type * as invoices from "../invoices.js";
import type * as journalEntries from "../journalEntries.js";
import type * as products from "../products.js";
import type * as quotes from "../quotes.js";
import type * as seed from "../seed.js";
import type * as suppliers from "../suppliers.js";
import type * as users from "../users.js";
import type * as workspaces from "../workspaces.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  accounts: typeof accounts;
  auth: typeof auth;
  bills: typeof bills;
  clients: typeof clients;
  companyProfiles: typeof companyProfiles;
  invoices: typeof invoices;
  journalEntries: typeof journalEntries;
  products: typeof products;
  quotes: typeof quotes;
  seed: typeof seed;
  suppliers: typeof suppliers;
  users: typeof users;
  workspaces: typeof workspaces;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
