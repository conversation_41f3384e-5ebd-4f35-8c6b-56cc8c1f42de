import React, { useState, useMemo } from 'react';
import { Client, NavigablePageProps } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import ActionModal from '../components/ui/ActionModal';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import ClientStatusSummary from '../components/clients/ClientStatusSummary';
import ClientFilterBar from '../components/clients/ClientFilterBar';
import ClientFormModal from '../components/clients/ClientFormModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

const Clients: React.FC<NavigablePageProps> = ({ workspaceId, navigate }) => {
  // Fetch data from Convex
  const allClients = useQuery(api.clients.getClients, { workspaceId: workspaceId as any });

  // Mutations
  const createClient = useMutation(api.clients.createClient);
  const updateClient = useMutation(api.clients.updateClient);
  const deleteClient = useMutation(api.clients.deleteClient);

  const isLoading = allClients === undefined;

  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // Modal states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [clientToDelete, setClientToDelete] = useState<Client | null>(null);
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [actionClient, setActionClient] = useState<Client | null>(null);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');

  const filteredClients = useMemo(() => {
    if (!allClients) return [];
    return allClients.filter(client => {
      const term = searchTerm.toLowerCase();
      return !term || client.name.toLowerCase().includes(term) || client.email.toLowerCase().includes(term);
    });
  }, [allClients, searchTerm]);

  const handleSave = async (clientData: Omit<Client, '_id' | '_creationTime' | 'workspaceId'>, id?: string) => {
    try {
      if (id) {
        await updateClient({
          clientId: id as any,
          name: clientData.name,
          email: clientData.email,
          phone: clientData.phone,
          address: clientData.address,
        });
      } else {
        await createClient({
          workspaceId: workspaceId as any,
          name: clientData.name,
          email: clientData.email,
          phone: clientData.phone,
          address: clientData.address,
        });
      }
      setIsFormOpen(false);
      setSelectedClient(null);
    } catch (error) {
      console.error('Failed to save client:', error);
    }
  };
  
  const handleNew = () => {
    setSelectedClient(null);
    setIsFormOpen(true);
  };

  const handleEdit = (client: Client) => {
    setSelectedClient(client);
    setIsFormOpen(true);
  };
  
  const handleDeleteRequest = (client: Client) => {
    setClientToDelete(client);
    setIsConfirmDeleteOpen(true);
  };

  const handleOpenActions = (client: Client) => {
    setActionClient(client);
    setIsActionModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (clientToDelete) {
      try {
        await deleteClient({ clientId: clientToDelete._id as any });
        setClientToDelete(null);
      } catch (error) {
        console.error('Failed to delete client:', error);
      }
    }
    setIsConfirmDeleteOpen(false);
  };

  const columns = [
    { key: 'name' as keyof Client, header: 'Name' },
    { key: 'email' as keyof Client, header: 'Email' },
    { key: 'phone' as keyof Client, header: 'Phone' },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Client) => (
        <div className="text-right">
          <Button
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0"
            onClick={() => handleOpenActions(item)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
            </svg>
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Clients</h2>
        <Button variant="primary" onClick={handleNew}>New Client</Button>
      </div>
      
      <ClientStatusSummary clients={allClients} isLoading={isLoading} />
      
      <ClientFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(5)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Client> columns={columns} data={filteredClients.map(c => ({...c, id: c._id}))} />
        )}
      </Card>
      
      <ClientFormModal 
        client={selectedClient}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedClient?._id)}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Client"
        message={
            <span>
                Are you sure you want to delete <strong>{clientToDelete?.name}</strong>?
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Client"
      />

      <ActionModal
        isOpen={isActionModalOpen}
        onClose={() => setIsActionModalOpen(false)}
        title={`Actions for ${actionClient?.name || 'Client'}`}
        actions={[
          {
            label: 'View Transactions',
            onClick: () => actionClient && navigate('Transactions', { filter: actionClient.name }),
            icon: (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            )
          },
          {
            label: 'Edit Client',
            onClick: () => actionClient && handleEdit(actionClient),
            icon: (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            )
          },
          {
            label: 'Delete Client',
            onClick: () => actionClient && handleDeleteRequest(actionClient),
            variant: 'danger' as const,
            icon: (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            )
          }
        ]}
      />
    </div>
  );
};

export default Clients;