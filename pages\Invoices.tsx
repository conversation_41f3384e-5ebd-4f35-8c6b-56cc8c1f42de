
import React, { useState, useCallback, useMemo } from 'react';
import { Invoice, InvoiceStatus, Client, Product, TaxRate, CompanyProfile, Account, JournalEntry } from '../types';
import Table from '../components/ui/Table';
import Button from '../components/ui/Button';
import Badge from '../components/ui/Badge';
import Card from '../components/ui/Card';
import InvoiceStatusSummary from '../components/invoices/InvoiceStatusSummary';
import InvoiceDetailModal from '../components/invoices/InvoiceDetailModal';
import InvoiceFormModal from '../components/invoices/InvoiceFormModal';
import Dropdown from '../components/ui/Dropdown';
import InvoiceFilterBar from '../components/invoices/InvoiceFilterBar';
import ConfirmationModal from '../components/ui/ConfirmationModal';
import AITransactionModal from '../components/transactions/AITransactionModal';
import { useQuery, useMutation } from "convex/react";
import { api } from "../convex/_generated/api";
import { isAIAvailable } from '../services/geminiService';

const createId = (prefix: string) => `${prefix}-${Math.random().toString(36).substr(2, 9)}`;

interface InvoicesProps {
  workspaceId: string;
}

export const Invoices: React.FC<InvoicesProps> = ({ workspaceId }) => {
  // Fetch data from Convex
  const allInvoices = useQuery(api.invoices.getInvoices, { workspaceId: workspaceId as any });
  const clients = useQuery(api.clients.getClients, { workspaceId: workspaceId as any });
  const products = useQuery(api.products.getProducts, { workspaceId: workspaceId as any });
  const accounts = useQuery(api.accounts.getAccounts, { workspaceId: workspaceId as any });
  const companyProfile = useQuery(api.companyProfiles.getCompanyProfile, { workspaceId: workspaceId as any });

  // Mutations
  const updateInvoiceStatus = useMutation(api.invoices.updateInvoiceStatus);
  const deleteInvoice = useMutation(api.invoices.deleteInvoice);
  const createInvoice = useMutation(api.invoices.createInvoice);
  const updateInvoice = useMutation(api.invoices.updateInvoice);

  const isLoading = allInvoices === undefined || clients === undefined || products === undefined || accounts === undefined || companyProfile === undefined;
  
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'All' | InvoiceStatus>('All');
  
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<Invoice | null>(null);

  const [isAITransactionModalOpen, setIsAITransactionModalOpen] = useState(false);
  const [initialAIPrompt, setInitialAIPrompt] = useState<string | undefined>(undefined);

  const filteredInvoices = useMemo(() => {
    if (!allInvoices || !clients) return [];
    return allInvoices
      .filter(invoice => statusFilter === 'All' || invoice.status === statusFilter)
      .filter(invoice => {
        const clientName = clients.find(c => c._id === invoice.clientId)?.name || '';
        const term = searchTerm.toLowerCase();
        return !term || clientName.toLowerCase().includes(term) || invoice._id.toLowerCase().includes(term);
      });
  }, [allInvoices, clients, searchTerm, statusFilter]);

  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const handleView = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setIsDetailOpen(true);
  };

  const handleNew = () => {
    setSelectedInvoice(null);
    setIsFormOpen(true);
  };

  const handleEdit = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setIsFormOpen(true);
  };

  const handleSave = async (invoiceData: Omit<Invoice, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>, id?: string) => {
    if (!clients || !products) return;

    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
    // For now, use a default tax rate since we don't have taxRates query yet
    const taxRateValue = 0.1; // 10% default tax
    const totalAmount = subtotal * (1 + taxRateValue);

    const dataToSave = {
      ...invoiceData,
      taxRate: taxRateValue,
      amount: totalAmount,
      workspaceId: workspaceId as any
    };

    try {
      if (id) {
        await updateInvoice({
          invoiceId: id as any,
          ...dataToSave
        });
      } else {
        await createInvoice({
          ...dataToSave,
          invoiceNumber: `INV-${Date.now().toString().slice(-6)}`
        });
      }
      setIsFormOpen(false);
    } catch (error) {
      console.error('Failed to save invoice:', error);
    }
  };

  const handleSendInvoice = async (invoice: Invoice) => {
    try {
      await updateInvoiceStatus({
        invoiceId: invoice._id as any,
        status: InvoiceStatus.Sent
      });
    } catch (error) {
      console.error('Failed to send invoice:', error);
    }
  };

  const handleMarkAsPaid = async (invoice: Invoice) => {
    try {
      await updateInvoiceStatus({
        invoiceId: invoice._id as any,
        status: InvoiceStatus.Paid
      });
    } catch (error) {
      console.error('Failed to mark invoice as paid:', error);
    }
  }

  const handleDeleteRequest = (invoice: Invoice) => {
    setInvoiceToDelete(invoice);
    setIsConfirmDeleteOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (invoiceToDelete) {
      try {
        await deleteInvoice({ invoiceId: invoiceToDelete._id as any });
        setInvoiceToDelete(null);
      } catch (error) {
        console.error('Failed to delete invoice:', error);
      }
    }
    setIsConfirmDeleteOpen(false);
  };
  
  const handleGenericAITransactionRequest = () => {
    setInitialAIPrompt(undefined);
    setIsAITransactionModalOpen(true);
  };

  const handleAITransactionRequest = (invoice: Invoice) => {
    const clientName = clients?.find(c => c._id === invoice.clientId)?.name || 'the client';
    const prompt = `Record payment received for invoice ${invoice._id.slice(-6).toUpperCase()} from ${clientName} for ${formatCurrency(invoice.amount)}.`;
    setInitialAIPrompt(prompt);
    setIsAITransactionModalOpen(true);
  };

  const handleJournalEntrySave = async (entryData: Omit<JournalEntry, '_id' | '_creationTime' | 'workspaceId'>) => {
     mockData.journalEntries.push({
        ...entryData,
        _id: createId('je'),
        _creationTime: Date.now(),
        workspaceId,
    });
    setIsAITransactionModalOpen(false);
    setInitialAIPrompt(undefined);
  };


  const columns = [
    { key: '_id' as keyof Invoice, header: 'Invoice ID', render: (item: Invoice) => item._id.slice(-6).toUpperCase() },
    {
      key: 'clientId' as keyof Invoice,
      header: 'Customer',
      render: (item: Invoice) => clients?.find(c => c._id === item.clientId)?.name || 'Unknown Client',
    },
    { key: 'issueDate' as keyof Invoice, header: 'Issued' },
    { key: 'dueDate' as keyof Invoice, header: 'Due' },
    {
      key: 'amount' as keyof Invoice,
      header: 'Amount',
      render: (item: Invoice) => <span className="font-medium">{formatCurrency(item.amount)}</span>,
    },
    {
      key: 'status' as keyof Invoice,
      header: 'Status',
      render: (item: Invoice) => <Badge status={item.status} />,
    },
    {
      key: 'actions' as 'actions',
      header: '',
      render: (item: Invoice) => (
        <div className="flex items-center justify-end space-x-2">
          <Button variant="secondary" size="sm" onClick={() => handleAITransactionRequest(item)} disabled={!isAIAvailable} title={!isAIAvailable ? "AI features unavailable" : "Record transaction using AI"}>
            Record AI
          </Button>
          <Dropdown
            trigger={
              <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" /></svg>
              </Button>
            }
          >
            <div className="py-1">
              {item.status === InvoiceStatus.Draft && (
                <button onClick={() => handleSendInvoice(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Send Invoice</button>
              )}
              {(item.status === InvoiceStatus.Sent || item.status === InvoiceStatus.Overdue) && (
                <button onClick={() => handleMarkAsPaid(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Mark as Paid</button>
              )}
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleView(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                View
              </button>
              <button onClick={() => handleEdit(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                Edit
              </button>
              <div className="border-t border-gray-100 my-1"></div>
              <button onClick={() => handleDeleteRequest(item)} className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-100">
                Delete
              </button>
            </div>
          </Dropdown>
        </div>
      )
    }
  ];
  
  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
        <h2 className="text-xl font-semibold text-gray-900">Invoices</h2>
        <div className="flex items-center gap-2">
          <Button variant="secondary" onClick={handleGenericAITransactionRequest} disabled={!isAIAvailable} title={aiDisabledTooltip}>Record with AI</Button>
          <Button variant="primary" onClick={handleNew}>New Invoice</Button>
        </div>
      </div>
      
      <InvoiceStatusSummary invoices={allInvoices} isLoading={isLoading} />
      
      <InvoiceFilterBar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
      />

      <Card>
        {isLoading ? (
          <div className="space-y-2 p-6">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            {[...Array(5)].map((_, i) => <div key={i} className="h-12 bg-gray-100 rounded animate-pulse"></div>)}
          </div>
        ) : (
          <Table<Invoice> columns={columns} data={filteredInvoices.map(i => ({...i, id: i._id}))} />
        )}
      </Card>

      <InvoiceDetailModal 
        invoice={selectedInvoice}
        isOpen={isDetailOpen}
        onClose={() => setIsDetailOpen(false)}
        client={clients?.find(c => c._id === selectedInvoice?.clientId)}
        companyProfile={companyProfile || null}
      />
      
      <InvoiceFormModal 
        invoice={selectedInvoice}
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSave={(data) => handleSave(data, selectedInvoice?._id)}
        clients={clients || []}
        products={products || []}
        taxRates={taxRates || []}
      />

      <AITransactionModal
        isOpen={isAITransactionModalOpen}
        onClose={() => setIsAITransactionModalOpen(false)}
        onSave={handleJournalEntrySave}
        accounts={accounts || []}
        workspaceId={workspaceId}
        initialPrompt={initialAIPrompt}
      />

      <ConfirmationModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Invoice"
        message={
            <span>
                Are you sure you want to delete invoice <strong>{invoiceToDelete?._id.slice(-6).toUpperCase()}</strong>? 
                This action cannot be undone.
            </span>
        }
        confirmText="Delete Invoice"
      />
    </div>
  );
};
