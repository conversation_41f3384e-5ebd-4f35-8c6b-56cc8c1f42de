import { v } from "convex/values";
import { mutation } from "./_generated/server";

// Seed initial data for a workspace
export const seedWorkspaceData = mutation({
  args: { 
    workspaceId: v.id("workspaces"),
    userId: v.id("users")
  },
  handler: async (ctx, args) => {
    // Check if workspace already has data
    const existingClients = await ctx.db
      .query("clients")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();

    if (existingClients.length > 0) {
      throw new Error("Workspace already has data");
    }

    // Create sample clients
    const client1Id = await ctx.db.insert("clients", {
      workspaceId: args.workspaceId,
      name: "Acme Corporation",
      email: "<EMAIL>",
      phone: "555-0123",
      address: "123 Business St, City, State 12345",
    });

    const client2Id = await ctx.db.insert("clients", {
      workspaceId: args.workspaceId,
      name: "TechStart Inc",
      email: "<EMAIL>",
      phone: "555-0456",
      address: "456 Innovation Ave, Tech City, TC 67890",
    });

    // Create sample suppliers
    const supplier1Id = await ctx.db.insert("suppliers", {
      workspaceId: args.workspaceId,
      name: "Office Supplies Co",
      email: "<EMAIL>",
      phone: "555-0789",
      address: "789 Supply Lane, Supply City, SC 11111",
    });

    const supplier2Id = await ctx.db.insert("suppliers", {
      workspaceId: args.workspaceId,
      name: "Cloud Services Inc",
      email: "<EMAIL>",
      phone: "555-0321",
      address: "321 Cloud St, Virtual City, VC 22222",
    });

    // Create sample products
    const product1Id = await ctx.db.insert("products", {
      workspaceId: args.workspaceId,
      name: "Web Development Service",
      description: "Custom web development services",
      price: 100,
      category: "Services",
      sku: "WEB-DEV-001",
      stockQuantity: 0,
    });

    const product2Id = await ctx.db.insert("products", {
      workspaceId: args.workspaceId,
      name: "UX Design Package",
      description: "Complete UX design package",
      price: 2500,
      category: "Services",
      sku: "UX-PKG-001",
      stockQuantity: 0,
    });

    const product3Id = await ctx.db.insert("products", {
      workspaceId: args.workspaceId,
      name: "Standard Keyboard",
      description: "Ergonomic office keyboard",
      price: 40,
      category: "Hardware",
      sku: "KB-STD-001",
      stockQuantity: 50,
    });

    // Get default tax rate
    const taxRate = await ctx.db
      .query("taxRates")
      .filter((q) => 
        q.and(
          q.eq(q.field("workspaceId"), args.workspaceId),
          q.eq(q.field("isDefault"), true)
        )
      )
      .first();

    // Create sample invoices
    const invoice1Id = await ctx.db.insert("invoices", {
      workspaceId: args.workspaceId,
      clientId: client1Id,
      invoiceNumber: "INV-001",
      issueDate: "2024-05-15",
      dueDate: "2024-06-14",
      amount: 5500,
      status: "Paid",
      items: [{
        productId: product1Id,
        description: "Web Development Services",
        quantity: 50,
        price: 100,
        total: 5000,
      }],
      taxId: taxRate?._id,
      taxRate: taxRate?.rate || 0.1,
    });

    const invoice2Id = await ctx.db.insert("invoices", {
      workspaceId: args.workspaceId,
      clientId: client2Id,
      invoiceNumber: "INV-002",
      issueDate: "2024-06-01",
      dueDate: "2024-07-01",
      amount: 2750,
      status: "Sent",
      items: [{
        productId: product2Id,
        description: "UX Design Package",
        quantity: 1,
        price: 2500,
        total: 2500,
      }],
      taxId: taxRate?._id,
      taxRate: taxRate?.rate || 0.1,
    });

    // Create sample bills
    const bill1Id = await ctx.db.insert("bills", {
      workspaceId: args.workspaceId,
      supplierId: supplier2Id,
      billNumber: "BILL-001",
      issueDate: "2024-06-01",
      dueDate: "2024-06-15",
      amount: 275,
      status: "Paid",
      items: [{
        description: "Monthly cloud subscription",
        quantity: 1,
        price: 250,
        total: 250,
      }],
      taxId: taxRate?._id,
      taxRate: taxRate?.rate || 0.1,
    });

    const bill2Id = await ctx.db.insert("bills", {
      workspaceId: args.workspaceId,
      supplierId: supplier1Id,
      billNumber: "BILL-002",
      issueDate: "2024-06-05",
      dueDate: "2024-07-05",
      amount: 880,
      status: "Unpaid",
      items: [{
        productId: product3Id,
        description: "Office keyboards",
        quantity: 20,
        price: 40,
        total: 800,
      }],
      taxId: taxRate?._id,
      taxRate: taxRate?.rate || 0.1,
    });

    // Create sample quotes
    const quote1Id = await ctx.db.insert("quotes", {
      workspaceId: args.workspaceId,
      clientId: client2Id,
      issueDate: "2024-06-02",
      expiryDate: "2024-07-02",
      amount: 1980,
      status: "Sent",
      items: [{
        productId: product1Id,
        description: "Web Development Services",
        quantity: 18,
        price: 100,
        total: 1800,
      }],
      taxId: taxRate?._id,
      taxRate: taxRate?.rate || 0.1,
    });

    return {
      message: "Sample data created successfully",
      counts: {
        clients: 2,
        suppliers: 2,
        products: 3,
        invoices: 2,
        bills: 2,
        quotes: 1,
      }
    };
  },
});
