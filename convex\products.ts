import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Get all products for a workspace
export const getProducts = query({
  args: { workspaceId: v.id("workspaces") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("products")
      .filter((q) => q.eq(q.field("workspaceId"), args.workspaceId))
      .collect();
  },
});

// Get a specific product
export const getProduct = query({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.productId);
  },
});

// Create a new product
export const createProduct = mutation({
  args: {
    workspaceId: v.id("workspaces"),
    name: v.string(),
    description: v.optional(v.string()),
    price: v.number(),
    category: v.optional(v.string()),
    sku: v.optional(v.string()),
    stockQuantity: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("products", {
      workspaceId: args.workspaceId,
      name: args.name,
      description: args.description,
      price: args.price,
      category: args.category,
      sku: args.sku,
      stockQuantity: args.stockQuantity,
    });
  },
});

// Update a product
export const updateProduct = mutation({
  args: {
    productId: v.id("products"),
    name: v.string(),
    description: v.optional(v.string()),
    price: v.number(),
    category: v.optional(v.string()),
    sku: v.optional(v.string()),
    stockQuantity: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.productId, {
      name: args.name,
      description: args.description,
      price: args.price,
      category: args.category,
      sku: args.sku,
      stockQuantity: args.stockQuantity,
    });
  },
});

// Delete a product
export const deleteProduct = mutation({
  args: { productId: v.id("products") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.productId);
  },
});

// Update stock quantity
export const updateStock = mutation({
  args: {
    productId: v.id("products"),
    quantity: v.number(),
  },
  handler: async (ctx, args) => {
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    const newQuantity = (product.stockQuantity || 0) + args.quantity;
    await ctx.db.patch(args.productId, {
      stockQuantity: Math.max(0, newQuantity),
    });
  },
});